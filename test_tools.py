#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Java工具是否可用
"""

import subprocess
import os

def test_tool(tool_name, args):
    """测试单个工具"""
    print(f"测试 {tool_name}...")
    
    try:
        # 方法1: 直接运行
        result = subprocess.run([tool_name] + args, 
                              capture_output=True, 
                              text=True,
                              timeout=10)
        print(f"  方法1 - 直接运行: 成功 (返回码: {result.returncode})")
        if result.stdout:
            print(f"  输出: {result.stdout[:100]}...")
        return True
        
    except FileNotFoundError:
        print(f"  方法1 - 直接运行: 失败 (FileNotFoundError)")
    except subprocess.TimeoutExpired:
        print(f"  方法1 - 直接运行: 超时 (但工具存在)")
        return True
    except Exception as e:
        print(f"  方法1 - 直接运行: 异常 ({e})")
    
    try:
        # 方法2: 使用where命令查找
        where_result = subprocess.run(["where", tool_name], 
                                    capture_output=True, 
                                    text=True)
        if where_result.returncode == 0:
            print(f"  方法2 - where命令: 找到工具")
            print(f"  路径: {where_result.stdout.strip()}")
            return True
        else:
            print(f"  方法2 - where命令: 未找到工具")
    except Exception as e:
        print(f"  方法2 - where命令: 异常 ({e})")
    
    try:
        # 方法3: 使用完整路径 (常见Java安装路径)
        java_paths = [
            r"C:\Program Files\Java\jdk*\bin",
            r"C:\Program Files (x86)\Java\jdk*\bin",
            r"C:\Program Files\Eclipse Adoptium\jdk*\bin",
            r"C:\Program Files\Microsoft\jdk*\bin"
        ]
        
        import glob
        for path_pattern in java_paths:
            paths = glob.glob(path_pattern)
            for path in paths:
                tool_path = os.path.join(path, f"{tool_name}.exe")
                if os.path.exists(tool_path):
                    print(f"  方法3 - 完整路径: 找到 {tool_path}")
                    return True
        
        print(f"  方法3 - 完整路径: 未找到")
    except Exception as e:
        print(f"  方法3 - 完整路径: 异常 ({e})")
    
    return False

def main():
    """主函数"""
    print("Java工具检测测试")
    print("=" * 50)
    
    # 显示环境信息
    print("环境信息:")
    print(f"  PATH: {os.environ.get('PATH', 'N/A')[:200]}...")
    print(f"  JAVA_HOME: {os.environ.get('JAVA_HOME', 'N/A')}")
    print()
    
    tools = {
        "java": ["-version"],
        "keytool": ["-help"],
        "jarsigner": ["-help"]
    }
    
    results = {}
    for tool, args in tools.items():
        results[tool] = test_tool(tool, args)
        print()
    
    print("=" * 50)
    print("测试结果:")
    for tool, success in results.items():
        status = "✓ 可用" if success else "✗ 不可用"
        print(f"  {tool}: {status}")
    
    if all(results.values()):
        print("\n所有工具都可用，APK加固程序应该能正常运行!")
    else:
        print("\n部分工具不可用，请检查Java JDK安装和PATH配置")

if __name__ == "__main__":
    main()
