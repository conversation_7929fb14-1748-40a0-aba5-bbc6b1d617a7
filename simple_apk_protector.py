#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版APK加固工具 - 专门处理已签名的APK
避免破坏APK结构，只添加保护标记
"""

import os
import sys
import subprocess
import zipfile
import shutil
import tempfile
from pathlib import Path
import hashlib
from datetime import datetime

class SimpleAPKProtector:
    def __init__(self, apk_path="myapp.apk"):
        self.apk_path = apk_path
        self.output_dir = Path("./output")
        self.keystore_path = Path("./keystore.jks")
        self.key_alias = "myapp_key"
        self.keystore_password = "123456"
        self.key_password = "123456"
        
        # 创建输出目录
        self.output_dir.mkdir(exist_ok=True)
    
    def check_dependencies(self):
        """检查必要的工具是否存在"""
        tools_config = {
            "java": ["-version"],
            "keytool": ["-help"],
            "jarsigner": ["-help"]
        }
        missing_tools = []
        
        for tool, args in tools_config.items():
            try:
                result = subprocess.run([tool] + args, 
                                      capture_output=True, 
                                      text=True,
                                      timeout=10)
                print(f"✓ {tool} 已安装")
            except FileNotFoundError:
                missing_tools.append(tool)
                print(f"✗ {tool} 未找到")
            except subprocess.TimeoutExpired:
                print(f"✓ {tool} 已安装")
            except Exception:
                try:
                    where_result = subprocess.run(["where", tool], 
                                                capture_output=True, 
                                                text=True)
                    if where_result.returncode == 0:
                        print(f"✓ {tool} 已安装")
                    else:
                        missing_tools.append(tool)
                        print(f"✗ {tool} 未找到")
                except:
                    missing_tools.append(tool)
                    print(f"✗ {tool} 未找到")
        
        if missing_tools:
            print(f"\n缺少必要工具: {', '.join(missing_tools)}")
            return False
        return True
    
    def generate_keystore(self):
        """生成签名密钥库"""
        if self.keystore_path.exists():
            print(f"密钥库已存在: {self.keystore_path}")
            return True
        
        print("正在生成签名密钥库...")
        
        cmd = [
            "keytool", "-genkeypair",
            "-keystore", str(self.keystore_path),
            "-alias", self.key_alias,
            "-keyalg", "RSA",
            "-keysize", "2048",
            "-validity", "10000",
            "-storepass", self.keystore_password,
            "-keypass", self.key_password,
            "-dname", "CN=MyApp,OU=Development,O=MyCompany,L=City,ST=State,C=CN"
        ]
        
        try:
            env = os.environ.copy()
            env['JAVA_TOOL_OPTIONS'] = '-Dfile.encoding=UTF-8'
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', env=env)
            if result.returncode == 0:
                print(f"✓ 密钥库生成成功: {self.keystore_path}")
                return True
            else:
                print(f"✗ 密钥库生成失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ 生成密钥库时出错: {e}")
            return False
    
    def add_protection_only(self):
        """仅添加保护标记，不修改APK结构"""
        print("开始轻量级APK加固...")
        
        if not Path(self.apk_path).exists():
            print(f"✗ APK文件不存在: {self.apk_path}")
            return False
        
        protected_apk = self.output_dir / "protected_myapp.apk"
        
        try:
            # 直接复制原APK
            shutil.copy2(self.apk_path, protected_apk)
            
            # 在APK中添加保护标记
            with zipfile.ZipFile(protected_apk, 'a') as zipf:
                # 生成保护数据
                protection_data = {
                    "protected": True,
                    "timestamp": datetime.now().isoformat(),
                    "version": "1.0",
                    "original_apk": str(self.apk_path)
                }
                
                import json
                protection_json = json.dumps(protection_data, ensure_ascii=False, indent=2)
                
                # 添加保护标记文件到assets目录
                zipf.writestr("assets/protection.dat", protection_json.encode('utf-8'))
                
                # 添加一个隐藏的保护标记
                zipf.writestr("META-INF/protection.info", protection_json.encode('utf-8'))
            
            print(f"✓ 轻量级加固完成: {protected_apk}")
            return str(protected_apk)
            
        except Exception as e:
            print(f"✗ 轻量级加固失败: {e}")
            return False
    
    def resign_apk(self, apk_path):
        """重新签名APK"""
        print(f"正在重新签名APK: {apk_path}")
        
        if not Path(apk_path).exists():
            print(f"✗ APK文件不存在: {apk_path}")
            return False
        
        if not self.keystore_path.exists():
            print("✗ 密钥库不存在，请先生成密钥库")
            return False
        
        # 创建重新签名的APK
        resigned_apk = str(Path(apk_path).with_suffix('.resigned.apk'))
        shutil.copy2(apk_path, resigned_apk)
        
        # 先删除原有签名
        print("  - 删除原有签名")
        with zipfile.ZipFile(apk_path, 'r') as source_zip:
            with zipfile.ZipFile(resigned_apk + '.tmp', 'w', zipfile.ZIP_DEFLATED) as target_zip:
                for item in source_zip.infolist():
                    # 跳过META-INF中的签名文件
                    if (item.filename.startswith('META-INF/') and 
                        (item.filename.endswith('.SF') or 
                         item.filename.endswith('.RSA') or 
                         item.filename.endswith('.DSA') or
                         item.filename.endswith('MANIFEST.MF'))):
                        continue
                    
                    data = source_zip.read(item.filename)
                    target_zip.writestr(item, data)
        
        # 替换文件
        os.replace(resigned_apk + '.tmp', resigned_apk)
        
        # 重新签名
        cmd = [
            "jarsigner",
            "-verbose",
            "-keystore", str(self.keystore_path),
            "-storepass", self.keystore_password,
            "-keypass", self.key_password,
            "-digestalg", "SHA-256",
            "-sigalg", "SHA256withRSA",
            resigned_apk,
            self.key_alias
        ]
        
        try:
            env = os.environ.copy()
            env['JAVA_TOOL_OPTIONS'] = '-Dfile.encoding=UTF-8'
            
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  encoding='utf-8', env=env)
            if result.returncode == 0:
                print(f"✓ APK重新签名成功: {resigned_apk}")
                return resigned_apk
            else:
                print(f"✗ APK重新签名失败: {result.stderr}")
                if Path(resigned_apk).exists():
                    Path(resigned_apk).unlink()
                return False
        except Exception as e:
            print(f"✗ 重新签名APK时出错: {e}")
            if Path(resigned_apk).exists():
                Path(resigned_apk).unlink()
            return False
    
    def verify_apk_installable(self, apk_path):
        """验证APK是否可安装"""
        print(f"正在验证APK可安装性: {apk_path}")
        
        try:
            with zipfile.ZipFile(apk_path, 'r') as zipf:
                # 检查关键文件
                file_list = zipf.namelist()
                
                if 'AndroidManifest.xml' not in file_list:
                    print("✗ 缺少AndroidManifest.xml")
                    return False
                
                if not any(f.endswith('.dex') for f in file_list):
                    print("✗ 缺少DEX文件")
                    return False
                
                # 尝试读取AndroidManifest.xml
                manifest_data = zipf.read('AndroidManifest.xml')
                if len(manifest_data) == 0:
                    print("✗ AndroidManifest.xml为空")
                    return False
                
                print("✓ APK结构检查通过")
                return True
                
        except Exception as e:
            print(f"✗ APK验证失败: {e}")
            return False
    
    def run(self):
        """运行简化的加固和重签名流程"""
        print("简化版APK加固工具")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 生成密钥库
        if not self.generate_keystore():
            return False
        
        # 轻量级加固（仅添加保护标记）
        protected_apk = self.add_protection_only()
        if not protected_apk:
            return False
        
        # 验证APK结构
        if not self.verify_apk_installable(protected_apk):
            print("✗ 加固后的APK可能无法安装")
            return False
        
        # 重新签名
        resigned_apk = self.resign_apk(protected_apk)
        if not resigned_apk:
            return False
        
        # 最终验证
        if not self.verify_apk_installable(resigned_apk):
            print("✗ 最终APK可能无法安装")
            return False
        
        print("\n" + "=" * 50)
        print("✓ 简化版APK加固完成!")
        print(f"✓ 输出文件: {resigned_apk}")
        print(f"✓ 密钥库: {self.keystore_path}")
        print("\n注意: 这是轻量级加固，仅添加了保护标记")
        
        return True

def main():
    """主函数"""
    protector = SimpleAPKProtector()
    success = protector.run()
    
    if success:
        print("\n程序执行成功!")
        sys.exit(0)
    else:
        print("\n程序执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
