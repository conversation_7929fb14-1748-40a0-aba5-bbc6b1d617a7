# APK加固和签名工具

这是一个Python程序，用于对Android APK文件进行加固处理和数字签名。

## 功能特性

- ✅ APK文件加固保护
- ✅ 自动生成签名密钥库
- ✅ APK数字签名
- ✅ 签名验证
- ✅ 生成签名信息文件

## 系统要求

- Python 3.6+
- Java JDK (包含keytool和jarsigner工具)

## 使用方法

1. 确保当前目录下有`myapp.apk`文件
2. 运行程序：
   ```bash
   python apk_protector.py
   ```

## 输出文件

程序运行后会在`output`目录下生成以下文件：

- `protected_myapp.signed.apk` - 加固并签名后的APK文件
- `signature_info.txt` - 签名信息文件
- `keystore.jks` - 签名密钥库文件（在根目录）

## 配置说明

程序使用以下默认配置：

- 密钥库密码: `123456`
- 密钥密码: `123456`
- 密钥别名: `myapp_key`
- 证书有效期: 10000天

如需修改配置，请编辑`apk_protector.py`文件中的相关参数。

## 注意事项

1. 请妥善保管生成的密钥库文件(`keystore.jks`)
2. 密钥库密码和密钥密码请根据实际需要修改
3. 首次运行会自动生成密钥库，后续运行会复用已有密钥库
4. 确保Java环境已正确安装并配置PATH

## 加固功能

程序实现的基本加固功能包括：

- 添加保护标记文件
- 计算文件完整性校验
- 基础资源混淆
- 防篡改检测

## 故障排除

如果遇到问题，请检查：

1. Java JDK是否正确安装
2. `myapp.apk`文件是否存在
3. 是否有足够的磁盘空间
4. 防病毒软件是否阻止了程序运行
