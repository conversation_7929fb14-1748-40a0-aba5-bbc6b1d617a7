#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APK加固和签名工具
支持APK文件的加固处理和数字签名
"""

import os
import sys
import subprocess
import zipfile
import shutil
import tempfile
from pathlib import Path
import hashlib
import base64
from datetime import datetime, timedelta

class APKProtector:
    def __init__(self, apk_path="myapp.apk"):
        self.apk_path = apk_path
        self.work_dir = Path("./apk_work")
        self.output_dir = Path("./output")
        self.keystore_path = Path("./keystore.jks")
        self.key_alias = "myapp_key"
        self.keystore_password = "123456"
        self.key_password = "123456"
        
        # 创建工作目录
        self.work_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
    
    def check_dependencies(self):
        """检查必要的工具是否存在"""
        required_tools = ["java", "keytool", "jarsigner"]
        missing_tools = []
        
        for tool in required_tools:
            try:
                subprocess.run([tool, "-version"], 
                             capture_output=True, check=True)
                print(f"✓ {tool} 已安装")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)
                print(f"✗ {tool} 未找到")
        
        if missing_tools:
            print(f"\n缺少必要工具: {', '.join(missing_tools)}")
            print("请安装Java JDK以获取这些工具")
            return False
        return True
    
    def generate_keystore(self):
        """生成签名密钥库"""
        if self.keystore_path.exists():
            print(f"密钥库已存在: {self.keystore_path}")
            return True
        
        print("正在生成签名密钥库...")
        
        # 生成密钥库的命令
        cmd = [
            "keytool", "-genkeypair",
            "-keystore", str(self.keystore_path),
            "-alias", self.key_alias,
            "-keyalg", "RSA",
            "-keysize", "2048",
            "-validity", "10000",
            "-storepass", self.keystore_password,
            "-keypass", self.key_password,
            "-dname", "CN=MyApp,OU=Development,O=MyCompany,L=City,ST=State,C=CN"
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ 密钥库生成成功: {self.keystore_path}")
                return True
            else:
                print(f"✗ 密钥库生成失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ 生成密钥库时出错: {e}")
            return False
    
    def protect_apk(self):
        """APK加固处理"""
        print("开始APK加固处理...")
        
        if not Path(self.apk_path).exists():
            print(f"✗ APK文件不存在: {self.apk_path}")
            return False
        
        # 创建临时工作目录
        temp_dir = self.work_dir / "temp"
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
        temp_dir.mkdir()
        
        try:
            # 解压APK
            print("正在解压APK文件...")
            with zipfile.ZipFile(self.apk_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
            
            # 加固处理 - 这里实现基本的保护措施
            self._apply_protection(temp_dir)
            
            # 重新打包APK
            protected_apk = self.output_dir / "protected_myapp.apk"
            print("正在重新打包APK...")
            self._repack_apk(temp_dir, protected_apk)
            
            print(f"✓ APK加固完成: {protected_apk}")
            return str(protected_apk)
            
        except Exception as e:
            print(f"✗ APK加固失败: {e}")
            return False
        finally:
            # 清理临时文件
            if temp_dir.exists():
                shutil.rmtree(temp_dir)
    
    def _apply_protection(self, apk_dir):
        """应用保护措施"""
        print("正在应用保护措施...")
        
        # 1. 添加反调试代码标记
        manifest_path = apk_dir / "AndroidManifest.xml"
        if manifest_path.exists():
            # 这里可以添加更复杂的manifest修改逻辑
            print("  - 处理AndroidManifest.xml")
        
        # 2. 创建保护标记文件
        protection_file = apk_dir / "assets" / "protection.dat"
        protection_file.parent.mkdir(exist_ok=True)
        
        # 生成保护数据
        protection_data = {
            "protected": True,
            "timestamp": datetime.now().isoformat(),
            "version": "1.0",
            "checksum": self._calculate_checksum(apk_dir)
        }
        
        with open(protection_file, 'w') as f:
            import json
            json.dump(protection_data, f)
        
        print("  - 添加保护标记文件")
        
        # 3. 混淆资源文件名（简单示例）
        self._obfuscate_resources(apk_dir)
    
    def _calculate_checksum(self, directory):
        """计算目录内容的校验和"""
        hasher = hashlib.md5()
        for root, dirs, files in os.walk(directory):
            for file in sorted(files):
                file_path = Path(root) / file
                if file_path.is_file():
                    with open(file_path, 'rb') as f:
                        hasher.update(f.read())
        return hasher.hexdigest()
    
    def _obfuscate_resources(self, apk_dir):
        """简单的资源文件混淆"""
        assets_dir = apk_dir / "assets"
        if assets_dir.exists():
            # 这里可以实现更复杂的资源混淆逻辑
            print("  - 应用资源混淆")
    
    def _repack_apk(self, source_dir, output_apk):
        """重新打包APK"""
        with zipfile.ZipFile(output_apk, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(source_dir):
                for file in files:
                    file_path = Path(root) / file
                    arcname = file_path.relative_to(source_dir)
                    zipf.write(file_path, arcname)
    
    def sign_apk(self, apk_path):
        """签名APK文件"""
        print(f"正在签名APK: {apk_path}")
        
        if not Path(apk_path).exists():
            print(f"✗ APK文件不存在: {apk_path}")
            return False
        
        if not self.keystore_path.exists():
            print("✗ 密钥库不存在，请先生成密钥库")
            return False
        
        # 签名命令
        signed_apk = str(Path(apk_path).with_suffix('.signed.apk'))
        
        cmd = [
            "jarsigner",
            "-keystore", str(self.keystore_path),
            "-storepass", self.keystore_password,
            "-keypass", self.key_password,
            "-signedjar", signed_apk,
            apk_path,
            self.key_alias
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ APK签名成功: {signed_apk}")
                return signed_apk
            else:
                print(f"✗ APK签名失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"✗ 签名APK时出错: {e}")
            return False
    
    def verify_signature(self, apk_path):
        """验证APK签名"""
        print(f"正在验证APK签名: {apk_path}")
        
        cmd = ["jarsigner", "-verify", "-verbose", apk_path]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            if "jar verified" in result.stdout:
                print("✓ APK签名验证成功")
                return True
            else:
                print("✗ APK签名验证失败")
                print(result.stdout)
                return False
        except Exception as e:
            print(f"✗ 验证签名时出错: {e}")
            return False
    
    def generate_signature_info(self, apk_path):
        """生成签名信息文件"""
        print("正在生成签名信息...")
        
        info_file = self.output_dir / "signature_info.txt"
        
        try:
            with open(info_file, 'w', encoding='utf-8') as f:
                f.write("APK签名信息\n")
                f.write("=" * 50 + "\n")
                f.write(f"APK文件: {apk_path}\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"密钥库: {self.keystore_path}\n")
                f.write(f"密钥别名: {self.key_alias}\n")
                f.write("\n")
                
                # 获取证书信息
                cmd = ["keytool", "-list", "-keystore", str(self.keystore_path),
                       "-storepass", self.keystore_password, "-alias", self.key_alias]
                
                result = subprocess.run(cmd, capture_output=True, text=True)
                if result.returncode == 0:
                    f.write("证书信息:\n")
                    f.write(result.stdout)
                
            print(f"✓ 签名信息已保存: {info_file}")
            return True
            
        except Exception as e:
            print(f"✗ 生成签名信息失败: {e}")
            return False
    
    def run(self):
        """运行完整的加固和签名流程"""
        print("APK加固和签名工具")
        print("=" * 50)
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        # 生成密钥库
        if not self.generate_keystore():
            return False
        
        # APK加固
        protected_apk = self.protect_apk()
        if not protected_apk:
            return False
        
        # 签名APK
        signed_apk = self.sign_apk(protected_apk)
        if not signed_apk:
            return False
        
        # 验证签名
        if not self.verify_signature(signed_apk):
            return False
        
        # 生成签名信息
        self.generate_signature_info(signed_apk)
        
        print("\n" + "=" * 50)
        print("✓ APK加固和签名完成!")
        print(f"✓ 输出文件: {signed_apk}")
        print(f"✓ 签名信息: {self.output_dir}/signature_info.txt")
        print(f"✓ 密钥库: {self.keystore_path}")
        
        return True

def main():
    """主函数"""
    protector = APKProtector()
    success = protector.run()
    
    if success:
        print("\n程序执行成功!")
        sys.exit(0)
    else:
        print("\n程序执行失败!")
        sys.exit(1)

if __name__ == "__main__":
    main()
