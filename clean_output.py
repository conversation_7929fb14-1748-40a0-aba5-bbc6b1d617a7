#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理输出文件，用于重新测试
"""

import os
import shutil
from pathlib import Path

def clean_output():
    """清理输出文件和目录"""
    print("正在清理输出文件...")
    
    # 要清理的文件和目录
    items_to_clean = [
        "output",
        "apk_work", 
        "keystore.jks",
        "signature_info.txt"
    ]
    
    for item in items_to_clean:
        path = Path(item)
        try:
            if path.is_file():
                path.unlink()
                print(f"✓ 删除文件: {item}")
            elif path.is_dir():
                shutil.rmtree(path)
                print(f"✓ 删除目录: {item}")
            else:
                print(f"- 跳过不存在的项目: {item}")
        except Exception as e:
            print(f"✗ 删除失败 {item}: {e}")
    
    print("清理完成!")

if __name__ == "__main__":
    clean_output()
